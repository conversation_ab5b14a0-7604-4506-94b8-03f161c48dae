* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --color-accent: #DE8559;
}


html {
  color-scheme: dark;
}


img {
  display: block;
  max-width: 100%;
}


body {
  font-size: 20px;
  font-family: system-ui;
  font-weight: 300;
}


h1 {
  font-size: 50px;
}

h2 {
  font-size: 30px;
}


.container {
  width: min(90%, 1200px);
  margin-inline: auto;
}


.u-accent {
  color: --var(--color-accent);
  font-weight: 700;
}


.cards-list {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
  justify-content: center;

  .card {
    --gap: 20px;
    text-align: center;
    border: 2px solid var(--color-accent);
    display: flex;
    flex-direction: column;
    gap: var(--gap);
    width: 400px;
    /* flex-grow: 1; */

    .card-info {
      display: flex;
      flex-direction: column;
      gap: var(--gap);
      padding: var(--gap);
    }

    .card-price {
      font-size: 30px;
      font-weight: 700;
    }

    .card-image {
      display: flex;
      justify-content: center;
      max-height: 150px;

      img {
        width: 100%;
        object-fit: cover;
        object-position: top;
      }
    }
  }
}